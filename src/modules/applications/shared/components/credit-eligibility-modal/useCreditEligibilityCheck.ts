import { useContext, useState } from 'react';
import { useHandleGenericError } from 'shared/hooks/alerts';
import { GlobalStateContext } from 'shared/hooks/state';

import { useUserEligibilityLazyQuery } from './queries.gen';
import {
  type CreditEligibilityCheckResponse,
  type CreditEligibilityFormData,
  CreditEligibilityStatus,
} from './types';

export const useCreditEligibilityCheck = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<
    CreditEligibilityCheckResponse | undefined
  >();
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string[]> | undefined
  >();
  const handleGenericError = useHandleGenericError();
  const { merchantId } = useContext(GlobalStateContext);
  const [getUserEligibility] = useUserEligibilityLazyQuery();

  const checkEligibility = async (data: CreditEligibilityFormData) => {
    setIsLoading(true);
    setValidationErrors(undefined);
    try {
      if (!merchantId) {
        throw new Error('Merchant ID is required');
      }

      const response = await getUserEligibility({
        variables: {
          merchant_id: merchantId,
          pin: data.idCode,
          email: data.email,
          conditions_agreement: data.privacyPolicyConsent,
          newsletter_agreement: data.newsletterConsent,
        },
        fetchPolicy: 'network-only',
      });

      const eligibilityResult = response.data?.user_eligibility;

      let status: CreditEligibilityStatus;

      switch (eligibilityResult) {
        case '1':
          status = CreditEligibilityStatus.ELIGIBLE;
          break;
        case '0':
          status = CreditEligibilityStatus.MORE_INFO_NEEDED;
          break;
        case '-1':
          status = CreditEligibilityStatus.NOT_ELIGIBLE;
          break;
        default:
          status = CreditEligibilityStatus.NOT_ELIGIBLE;
      }

      const parsedResponse: CreditEligibilityCheckResponse = {
        status,
        customerName: `Customer (${data.idCode})`,
      };

      setResult(parsedResponse);
      return parsedResponse;
    } catch (error: unknown) {
      // Check if it's a GraphQL validation error
      if (error && typeof error === 'object' && 'graphQLErrors' in error) {
        const graphQLError = error as {
          graphQLErrors: Array<{
            message: string;
            validation?: Record<string, string[]>;
          }>;
        };
        const validationError = graphQLError.graphQLErrors.find(
          (err) => err.message === 'validation' && err.validation,
        );

        if (validationError) {
          setValidationErrors(validationError.validation);
          return;
        }
      }

      handleGenericError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const resetResult = () => {
    setResult(undefined);
    setValidationErrors(undefined);
  };

  return {
    isLoading,
    result,
    validationErrors,
    checkEligibility,
    resetResult,
  };
};
