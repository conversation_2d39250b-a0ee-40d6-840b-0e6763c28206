import { Box, HStack, Icon, Text, VStack } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { FiAlertTriangle, FiCheckCircle, FiInfo } from 'react-icons/fi';
import {
  LocizeNamespaces,
  LocizeTerminalKeys,
} from 'shared/constants/localization-keys';

import {
  type CreditEligibilityCheckResponse,
  CreditEligibilityStatus,
} from './types';

interface CreditEligibilityResultProps {
  result: CreditEligibilityCheckResponse;
}

export const CreditEligibilityResult = ({
  result,
}: CreditEligibilityResultProps) => {
  const { t } = useTranslation(LocizeNamespaces.TERMINAL);

  const getLocalizedMessage = (status: CreditEligibilityStatus) => {
    switch (status) {
      case CreditEligibilityStatus.ELIGIBLE:
        return t(LocizeTerminalKeys.CREDIT_ELIGIBILITY_RESULT_ELIGIBLE);
      case CreditEligibilityStatus.NOT_ELIGIBLE:
        return t(LocizeTerminalKeys.CREDIT_ELIGIBILITY_RESULT_NOT_ELIGIBLE);
      case CreditEligibilityStatus.MORE_INFO_NEEDED:
        return t(LocizeTerminalKeys.CREDIT_ELIGIBILITY_RESULT_MORE_INFO_NEEDED);
      default:
        return t(LocizeTerminalKeys.CREDIT_ELIGIBILITY_RESULT_NOT_ELIGIBLE);
    }
  };

  const resultConfigs = {
    [CreditEligibilityStatus.ELIGIBLE]: {
      bgColor: 'green.100',
      iconColor: 'green.900',
      textColor: 'green.900',
      icon: FiCheckCircle,
    },
    [CreditEligibilityStatus.MORE_INFO_NEEDED]: {
      bgColor: 'blue.100',
      iconColor: 'blue.900',
      textColor: 'blue.900',
      icon: FiInfo,
    },
    [CreditEligibilityStatus.NOT_ELIGIBLE]: {
      bgColor: 'red.100',
      iconColor: 'red.900',
      textColor: 'red.900',
      icon: FiAlertTriangle,
    },
  };

  const config = resultConfigs[result.status] || {
    bgColor: 'neutral.50',
    iconColor: 'neutral.700',
    textColor: 'neutral.700',
    icon: FiInfo,
  };

  return (
    <Box bg={config.bgColor} borderRadius="8px" p={4} w="full">
      <HStack spacing={4} align="flex-start">
        <Box pt={1}>
          <Icon as={config.icon} boxSize={6} color={config.iconColor} />
        </Box>
        <VStack spacing={2} align="flex-start" flex={1}>
          {result.customerName && (
            <Text
              fontSize="16px"
              fontWeight="400"
              color={
                result.status === CreditEligibilityStatus.NOT_ELIGIBLE
                  ? 'red.900'
                  : config.textColor
              }
              lineHeight="1.5"
            >
              <Text as="span" fontWeight="600">
                {result.customerName.split(' (')[0]}
              </Text>
              {result.customerName.includes(' (') && (
                <Text as="span" fontWeight="400">
                  {' (' + result.customerName.split(' (')[1]}
                </Text>
              )}
            </Text>
          )}
          <Text
            fontSize="16px"
            fontWeight="400"
            color={config.textColor}
            lineHeight="1.5"
          >
            {getLocalizedMessage(result.status)}
          </Text>
        </VStack>
      </HStack>
    </Box>
  );
};
